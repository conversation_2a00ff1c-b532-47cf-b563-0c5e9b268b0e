package com.ms.bp.application;

import com.ms.bp.shared.util.LambdaResourceManager;
import com.ms.bp.domain.permission.model.UserPermissionInfo;
import com.ms.bp.domain.permission.model.JobCombination;
import com.ms.bp.domain.permission.model.Permission;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.domain.master.repository.SystemAdminRepository;
import com.ms.bp.domain.master.model.UnitGroupInfo;
import com.ms.bp.interfaces.dto.response.UserPermissionsResponseV2;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.util.PermissionCodeParser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Stream;

/**
 * 権限アプリケーションサービス
 * 権限関連のユースケースを調整し、ドメインサービスを組み合わせて処理を実行する
 * 権限は読み取り専用操作のため、読み取り専用実行を使用する
 */
public class PermissionApplicationService {
    private static final Logger logger = LoggerFactory.getLogger(PermissionApplicationService.class);
    /** MS戦略・コーポOF判定コード */
    private static final String MS_STRATEGY_CORPO_HANT_CODE = "2";

    // 役職区分名称マッピング
    private static final Map<String, String> POSITION_NAME_MAPPING = Map.of(
            "41","ＧＭ、ＡＭ、室長",
            "51","ＵＬ、ＤＣ長",
            "61","非役職者"
    );


    /**
     * ユーザーの全権限を取得
     * ロールリスト構造のレスポンスを返却する
     *
     * @param userInfo 認証済みユーザー情報
     * @param unitCode ユニットコード
     * @param positionCode 役職区分コード
     * @param areaCode エリアコード
     * @param groupCode グループコード
     * @param systemAdminFlag システム管理者フラグ（"1":管理者、その他:一般）
     * @param operationType 操作種別
     * @return ユーザー権限レスポンス
     */
    public UserPermissionsResponseV2 getUserPermissions(UserInfo userInfo,
                                                       String unitCode,
                                                       String positionCode,
                                                       String areaCode,
                                                       String groupCode,
                                                       String systemAdminFlag,
                                                       String operationType) {
        logger.info("新業務要件権限取得開始: ユーザーID={}, systemAdminFlag={}",
                   userInfo.getShainCode(), systemAdminFlag);

        // ユーザーのユニットコード、役職区分コード、エリアコード、グループコードを取得
        JobCombination primaryJob = getPrimaryJobCombination(userInfo, unitCode, positionCode, areaCode, groupCode);

        // システム管理者判定
        boolean isSystemAdmin = determineSystemAdmin(userInfo, systemAdminFlag, unitCode, positionCode, areaCode, groupCode);

        if (isSystemAdmin) {
            // システム管理者の場合
            return createSystemAdminResponse(userInfo, primaryJob, operationType);
        } else {
            // 一般ユーザーの場合：兼務情報を含むロールリストを作成
            List<JobCombination> allJobCombinations = getAllJobCombinations(userInfo, primaryJob);
            return createRoleBasedResponse(userInfo, allJobCombinations, operationType);
        }
    }

    /**
     * 主職務の組み合わせを取得
     * パラメータが空の場合は社員マスタから動的取得
     */
    private JobCombination getPrimaryJobCombination(UserInfo userInfo, String unitCode, String positionCode, String areaCode, String groupCode) {

        // パラメータが提供されている場合はそれを使用
        if (isParametersProvided(unitCode, positionCode, areaCode, groupCode)) {
            return JobCombination.builder()
                    .unitCode(unitCode)
                    .positionCode(positionCode)
                    .areaCode(areaCode)
                    .groupCode(groupCode)
                    .build();
        }

        // パラメータが空の場合、社員マスタから取得
        return LambdaResourceManager.executeReadOnly(serviceFactory ->
                        serviceFactory.getUserMasterRepository()
                                .findUserBasicInfo(
                                        userInfo.getSystemOperationCompanyCode(),
                                        userInfo.getShainCode()))
                .map(basicInfo -> JobCombination.builder()
                        .unitCode(basicInfo.getUnitCode())
                        .positionCode(basicInfo.getPositionCode())
                        .areaCode(basicInfo.getAreaCode())
                        .groupCode(basicInfo.getGroupCode())
                        .build())
                .orElse(null);
    }

    /**
     * システム管理者用レスポンス作成
     */
    private UserPermissionsResponseV2 createSystemAdminResponse(UserInfo userInfo, JobCombination primaryJob, String operationType) {
        logger.info("システム管理者レスポンス作成開始: 社員コード={}", userInfo.getShainCode());

        // 全ての有効な本社権限を取得 & 変換 & フィルタリング（一括処理）
        var permissionList = LambdaResourceManager.executeReadOnly(serviceFactory ->
                        serviceFactory.getPermissionRepository()
                                .findAllValidHeadOfficePermissions(userInfo.getSystemOperationCompanyCode()))
                .stream()
                .map(this::convertToPermissionInfo)
                .filter(p -> switch (operationType) {
                    case BusinessConstants.OPERATION_UPLOAD_CODE,
                         BusinessConstants.OPERATION_DOWNLOAD_CODE -> operationType.equals(p.getOperationDivision());
                    case null, default -> true;
                })
                .toList();

        // ロール情報を作成
        var roleInfo = createRoleInfo(primaryJob, permissionList, userInfo.getSystemOperationCompanyCode());

        return UserPermissionsResponseV2.builder()
                .systemOperationCompanyCode(userInfo.getSystemOperationCompanyCode())
                .shainCode(userInfo.getShainCode())
                .screenDisplayFlag("0") // 表示可
                .systemAdminFlag("1") // システム管理者
                .roleList(List.of(roleInfo))
                .build();
    }

    /**
     * ロール情報を作成
     */
    private UserPermissionsResponseV2.RoleInfo createRoleInfo(JobCombination job, List<UserPermissionsResponseV2.PermissionInfo> permissions,
                                                              String systemOperationCompanyCode) {

        // ユニット・グループ情報を取得
        var unitGroupInfo = getUnitGroupInfo(job.getUnitCode());

        // MS戦略・コーポOF判定コードに対応する種類コードリストを取得
        var ruleType = LambdaResourceManager.executeReadOnly(serviceFactory ->
                serviceFactory.getPermissionRepository()
                        .findRuleTypeCodesByMsStrategyCorp(MS_STRATEGY_CORPO_HANT_CODE, systemOperationCompanyCode));

        // ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外
        var isRuleType = !ruleType.isEmpty() && !ruleType.contains(job.getUnitCode());

        // 役職区分判定要否 0:否 1:要
        var positionSpecialCheck = needsSpecialCheck(permissions, isRuleType);

        // エリアリストを作成
        var areaList = createAreaList(permissions, systemOperationCompanyCode, job.getAreaCode());

        return UserPermissionsResponseV2.RoleInfo.builder()
                .areaCode(job.getAreaCode())
                .unitCode(job.getUnitCode())
                .unitName(unitGroupInfo.getUnitName())
                .groupCode(unitGroupInfo.getGroupCode())
                .groupName(unitGroupInfo.getGroupName())
                .positionSpecialCheck(positionSpecialCheck)
                .positionCode(job.getPositionCode())
                .positionName(getPositionName(job.getPositionCode()))
                .permissionList(permissions)
                .areaList(areaList)
                .build();
    }

    /**
     * エリアリストを作成
     */
    private List<UserPermissionsResponseV2.AreaInfo> createAreaList(List<UserPermissionsResponseV2.PermissionInfo> permissions,
                                                                    String systemOperationCompanyCode,
                                                                    String primaryAreaCode) {
        // エリア担当者権限があるかチェック
        var hasAreaSpecificPermission = permissions.stream()
                .anyMatch(p -> BusinessConstants.AREA_PATTERN_AREA_SPECIFIC.equals(p.getAreaPattern()));

        return hasAreaSpecificPermission
                ? // エリア担当者権限がある場合、権限ルールマスタから取得
                LambdaResourceManager.executeReadOnly(serviceFactory ->
                                serviceFactory.getPermissionRepository()
                                        .findAreaInfosByAreaTantoshaPermission(systemOperationCompanyCode))
                        .stream()
                        .map(areaInfo -> UserPermissionsResponseV2.AreaInfo.builder()
                                .areaCode(areaInfo.getAreaCode())
                                .areaName(areaInfo.getAreaName())
                                .build())
                        .toList()
                : // エリアの情報のみ
                List.of(UserPermissionsResponseV2.AreaInfo.builder()
                        .areaCode(primaryAreaCode)
                        .areaName(LambdaResourceManager.executeReadOnly(serviceFactory ->
                                serviceFactory.getAreaCodeRepository()
                                        .findAreaNameByAreaCode(primaryAreaCode)
                                        .orElse("")))
                        .build());
    }

    /**
     * 指定されたルールに基づき、特別なチェック（役職区分判定要否）が必要かどうかを判定します。
     *
     * @param permissions ユーザーの権限リスト
     * @return "1" (要), "0" (否)
     */
    public static String needsSpecialCheck(List<UserPermissionsResponseV2.PermissionInfo> permissions, boolean isRuleType) {
        // 条件：ファイルタイプが "002"/"003" かつ エリア権限（3桁目 'A'） かつ ユーザがMS_戦略・コーポOFに含まれるユニットに所属している以外場合
        return permissions.stream()
                .anyMatch(p -> {
                    var code = p.getPermissionCode();
                    return (BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE.equals(p.getFileType()) || BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE.equals(p.getFileType())) &&
                            code != null && code.length() > 2 && code.charAt(2) == 'A' && isRuleType;
                }) ? "1" : "0";
    }

    /**
     * パラメータが提供されているかチェック
     */
    private boolean isParametersProvided(String unitCode, String positionCode, String areaCode, String groupCode) {
        return Stream.of(unitCode, positionCode, areaCode, groupCode)
                .allMatch(StringUtils::isNotBlank);
    }

    /**
     * 全ての職務組み合わせ（主職務+兼務）を取得
     */
    private List<JobCombination> getAllJobCombinations(UserInfo userInfo, JobCombination primaryJob) {
        // 兼務情報を取得して JobCombination に変換
        var concurrentJobs = LambdaResourceManager.executeReadOnly(serviceFactory ->
                        serviceFactory.getConcurrentJobRepository()
                                .findConcurrentJobsWithAreaInfo(userInfo.getShainCode(), userInfo.getSystemOperationCompanyCode()))
                .stream()
                .map(concurrentJob -> JobCombination.builder()
                        .unitCode(concurrentJob.getUnitCode())
                        .positionCode(concurrentJob.getPositionDivision())
                        .areaCode(concurrentJob.getAreaCode())
                        .groupCode(concurrentJob.getGroupCode())
                        .build())
                .toList();

        // 主職務と兼務を結合
        var allJobs = Stream.concat(
                Stream.of(primaryJob),
                concurrentJobs.stream()
        ).distinct().toList();

        logger.debug("職務組み合わせ取得完了: 主職務=1, 兼務={}, 合計={}", concurrentJobs.size(), allJobs.size());
        return allJobs;
    }

    /**
     * 指定職務組み合わせの権限を取得
     */
    private List<UserPermissionInfo> getPermissionsForJobCombination(UserInfo userInfo, JobCombination job) {
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            var permissionService = serviceFactory.createPermissionService();

            // 一時的なUserInfoを作成（職務組み合わせの情報を設定）
            var tempUserInfo = new UserInfo();
            tempUserInfo.setShainCode(userInfo.getShainCode());
            tempUserInfo.setSystemOperationCompanyCode(userInfo.getSystemOperationCompanyCode());
            tempUserInfo.setUnitCode(job.getUnitCode());
            tempUserInfo.setPositionCode(job.getPositionCode());
            tempUserInfo.setAreaCode(job.getAreaCode());
            tempUserInfo.setGroupCode(job.getGroupCode());

            return permissionService.getUserPermissions(tempUserInfo);
        });
    }


    /**
     * ユニットコードからユニット・グループ統合情報を取得（パフォーマンス最適化版）
     * 単一クエリでユニット名とグループ名を同時取得
     */
    private UnitGroupInfo getUnitGroupInfo(String unitCode) {
        return LambdaResourceManager.executeReadOnly(serviceFactory ->
                serviceFactory.getUnitMasterRepository()
                    .findUnitGroupInfoByUnitCode(unitCode)
                    .orElse(UnitGroupInfo.builder().unitCode(unitCode).unitName("").groupCode("").groupName("").build()));
    }

    /**
     * 役職区分コードから役職区分名を取得
     */
    private String getPositionName(String positionCode) {
        return positionCode == null || positionCode.isBlank()
                ? ""
                : POSITION_NAME_MAPPING.getOrDefault(positionCode, "");
    }

    /**
     * システム管理者判定ロジック
     */
    private boolean determineSystemAdmin(UserInfo userInfo, String systemAdminFlag,
                                        String unitCode, String positionCode, String areaCode, String groupCode) {
        // パラメータが非空かつシステム管理者フラグが"1"の場合
        if ("1".equals(systemAdminFlag)) {
            return isParametersProvided(unitCode, positionCode, areaCode, groupCode);
        }

        // パラメータシステム管理者フラグが"1"以外の場合、データベースで判定
        return LambdaResourceManager.executeReadOnly(serviceFactory -> {
            SystemAdminRepository systemAdminRepository = serviceFactory.getSystemAdminRepository();
            return systemAdminRepository.isValidSystemAdmin(userInfo.getShainCode(), userInfo.getSystemOperationCompanyCode());
        });
    }

    /**
     * 一般ユーザー用ロールベースレスポンス作成
     */
    private UserPermissionsResponseV2 createRoleBasedResponse(UserInfo userInfo, List<JobCombination> allJobCombinations, String operationType) {
        logger.debug("ロールベースレスポンス作成開始: 職務組み合わせ数={}", allJobCombinations.size());

        var isUploadOrDownload = Set.of(BusinessConstants.OPERATION_UPLOAD_CODE,
                BusinessConstants.OPERATION_DOWNLOAD_CODE).contains(operationType);

        var roleList = allJobCombinations.stream()
                .map(job -> {
                    var jobPermissions = getPermissionsForJobCombination(userInfo, job).stream()
                            .filter(p -> !isUploadOrDownload || operationType.equals(p.getOperationDivision()))
                            .toList();

                    if (jobPermissions.isEmpty()) {
                        return null;
                    }

                    var permissionList = jobPermissions.stream()
                            .map(p -> UserPermissionsResponseV2.PermissionInfo.builder()
                                    .permissionCode(p.getPermissionCode())
                                    .operationDivision(p.getOperationDivision())
                                    .areaPattern(p.getAreaPattern())
                                    .fileType(p.getFileType())
                                    .build())
                            .toList();

                    return createRoleInfo(job, permissionList, userInfo.getSystemOperationCompanyCode());
                })
                .filter(Objects::nonNull)
                .toList();

        return UserPermissionsResponseV2.builder()
                .systemOperationCompanyCode(userInfo.getSystemOperationCompanyCode())
                .shainCode(userInfo.getShainCode())
                .screenDisplayFlag(roleList.isEmpty() ? "0" : "1") // 権限がある場合は表示可
                .systemAdminFlag("0") // 一般ユーザー
                .roleList(roleList)
                .build();
    }



    /**
     * Permissionを新構造のPermissionInfoに変換
     * 共通の権限コード解析ロジックを使用してコード重複を排除
     *
     * @param permission 権限オブジェクト
     * @return 新構造の権限情報DTO
     */
    private UserPermissionsResponseV2.PermissionInfo convertToPermissionInfo(Permission permission) {
        String permissionCode = permission.getPermissionCode();

        // 共通解析ロジックを使用
        PermissionCodeParser.ParsedPermissionCode parsed = PermissionCodeParser.parse(permissionCode);

        // 新レスポンス構造では数値形式を使用
        return UserPermissionsResponseV2.PermissionInfo.builder()
            .permissionCode(parsed.getPermissionCode())
            .fileType(parsed.getFileTypeCode())           // 数値形式（1-4）
            .operationDivision(parsed.getOperationDivision()) // 数値形式（0=UL, 1=DL）
            .areaPattern(parsed.getAreaPattern())     // 数値形式（1=H, 2=A）
            .build();
    }

}
