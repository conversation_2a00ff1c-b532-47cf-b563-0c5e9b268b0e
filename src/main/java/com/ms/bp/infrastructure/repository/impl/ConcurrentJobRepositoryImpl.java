package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.ConcurrentJobInfo;
import com.ms.bp.domain.master.repository.ConcurrentJobRepository;
import com.ms.bp.infrastructure.repository.dao.ConcurrentJobDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;


/**
 * 兼務マスタリポジトリ実装クラス
 * 領域インターフェースと基盤設備層DAOの橋渡しを行う
 */
public class ConcurrentJobRepositoryImpl implements ConcurrentJobRepository {
    private static final Logger logger = LoggerFactory.getLogger(ConcurrentJobRepositoryImpl.class);

    private final ConcurrentJobDataAccess dataAccess;

    public ConcurrentJobRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new ConcurrentJobDataAccess(jdbcTemplate);
    }

    @Override
    public List<ConcurrentJobInfo> findValidConcurrentJobs(String shainCode, String systemOperationCompanyCode) {
        try {
            logger.debug("有効兼務情報取得開始: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode);
            
            var result = dataAccess.findValidConcurrentJobs(shainCode, systemOperationCompanyCode);
            
            logger.debug("有効兼務情報取得完了: 件数={}", result.size());
            return result;
            
        } catch (SQLException e) {
            logger.error("有効兼務情報取得中にエラーが発生しました: shainCode={}, systemOperationCompanyCode={}", 
                        shainCode, systemOperationCompanyCode, e);
            throw new RuntimeException("有効兼務情報取得処理でエラーが発生しました", e);
        }
    }

    @Override
    public List<ConcurrentJobInfo> findConcurrentJobsWithAreaInfo(String shainCode, String systemOperationCompanyCode) {
        try {
            logger.debug("兼務情報（エリア・グループ付き）取得開始: shainCode={}, systemOperationCompanyCode={}",
                        shainCode, systemOperationCompanyCode);

            var result = dataAccess.findConcurrentJobsWithAreaInfo(shainCode, systemOperationCompanyCode);

            logger.debug("兼務情報（エリア・グループ付き）取得完了: 件数={}", result.size());
            return result;

        } catch (SQLException e) {
            logger.error("兼務情報（エリア・グループ付き）取得中にエラーが発生しました: shainCode={}, systemOperationCompanyCode={}",
                        shainCode, systemOperationCompanyCode, e);
            throw new RuntimeException("兼務情報（エリア・グループ付き）取得処理でエラーが発生しました", e);
        }
    }
}