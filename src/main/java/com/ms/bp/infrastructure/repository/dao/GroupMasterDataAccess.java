package com.ms.bp.infrastructure.repository.dao;

import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.List;
import java.util.Optional;

/**
 * グループマスタデータアクセス実装
 * M_GROUPMST（グループマスタ）への具体的なデータアクセスを実装
 */
public class GroupMasterDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(GroupMasterDataAccess.class);

    // SQL定数
    private static final String EXISTS_BY_GROUP_CODE_SQL = """
        SELECT 1
        FROM M_GROUPMST
        WHERE GROUP_CODE = ?
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
        AND SHIYO_KNSH_KUBUN = '0'
        LIMIT 1
        """;

    private static final String FIND_BY_GROUP_CODE_SQL = """
        SELECT
            g.GROUP_CODE,
            g.GROUP_MEI,
            g.HKMT_UNIT_CODE
        FROM M_GROUPMST g
        INNER JOIN M_UNITMST u ON g.HKMT_UNIT_CODE = u.UNIT_CODE
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= u.SHRYB
            AND u.SHIYO_KNSH_KUBUN = '0'
        WHERE g.GROUP_CODE = ?
        AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= g.SHRYB
        AND g.SHIYO_KNSH_KUBUN = '0'
        ORDER BY g.KSHB DESC
        LIMIT 1
        """;

    private final JdbcTemplate jdbcTemplate;

    public GroupMasterDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * グループコードでグループマスタの存在チェック
     *
     * @param groupCode グループコード
     * @return 存在する場合true、存在しない場合false
     * @throws SQLException データベースアクセスエラー
     */
    public boolean existsByGroupCode(String groupCode) throws SQLException {
        logger.debug("グループマスタ存在チェック開始: グループコード={}", groupCode);

        List<Integer> results = jdbcTemplate.query(
            EXISTS_BY_GROUP_CODE_SQL,
            new Object[]{groupCode},
            rs -> rs.getInt(1)
        );

        boolean exists = !results.isEmpty();
        logger.debug("グループマスタ存在チェック完了: グループコード={}, 存在={}", groupCode, exists);

        return exists;
    }

    /**
     * グループコードでグループマスタ情報を取得
     * 指定されたグループコードに対応するグループ情報を取得する
     * 関連するユニットマスタも有効である必要がある
     *
     * @param groupCode グループコード
     * @return グループマスタ情報（該当データがない場合は空のOptional）
     * @throws SQLException データベースアクセスエラー
     */
    public Optional<GroupMasterInfo> findByGroupCode(String groupCode) throws SQLException {
        logger.debug("グループマスタ情報取得開始: グループコード={}", groupCode);

        List<GroupMasterInfo> results = jdbcTemplate.query(
            FIND_BY_GROUP_CODE_SQL,
            new Object[]{groupCode},
            rs -> GroupMasterInfo.builder()
                .groupCode(rs.getString("GROUP_CODE"))
                .groupName(rs.getString("GROUP_MEI"))
                .inheritanceUnitCode(rs.getString("HKMT_UNIT_CODE"))
                .build()
        );

        Optional<GroupMasterInfo> result = results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));

        if (result.isPresent()) {
            logger.debug("グループマスタ情報取得完了: グループコード={}, グループ名={}",
                        groupCode, result.get().getGroupName());
        } else {
            logger.debug("グループマスタ情報が見つかりませんでした: グループコード={}", groupCode);
        }

        return result;
    }
}
