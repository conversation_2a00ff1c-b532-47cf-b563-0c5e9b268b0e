package com.ms.bp.infrastructure.repository.impl;

import com.ms.bp.domain.master.model.GroupMasterInfo;
import com.ms.bp.domain.master.repository.GroupMasterRepository;
import com.ms.bp.infrastructure.repository.dao.GroupMasterDataAccess;
import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Optional;

/**
 * ユーザーマスタリポジトリ実装
 * M_GROUPMST（グループマスタ）へのデータアクセスを実装
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public class GroupMasterRepositoryImpl implements GroupMasterRepository {
    private static final Logger logger = LoggerFactory.getLogger(GroupMasterRepositoryImpl.class);

    private final GroupMasterDataAccess dataAccess;

    public GroupMasterRepositoryImpl(JdbcTemplate jdbcTemplate) {
        this.dataAccess = new GroupMasterDataAccess(jdbcTemplate);
    }

    @Override
    public boolean existsByGroupCode(String groupCode) {
        try {
            boolean exists = dataAccess.existsByGroupCode(groupCode);
            logger.debug("グループマスタ存在チェックを実行しました: グループコード={}, 存在={}", groupCode, exists);
            return exists;
        } catch (SQLException e) {
            logger.error("グループマスタ存在チェック中にエラーが発生しました: グループコード={}", groupCode, e);
            throw new RuntimeException("グループマスタ存在チェックに失敗しました", e);
        }
    }

    @Override
    public Optional<GroupMasterInfo> findByGroupCode(String groupCode) {
        try {
            Optional<GroupMasterInfo> result = dataAccess.findByGroupCode(groupCode);
            if (result.isPresent()) {
                logger.debug("グループマスタ情報を取得しました: グループコード={}, グループ名={}",
                           groupCode, result.get().getGroupName());
            } else {
                logger.debug("グループマスタ情報が見つかりませんでした: グループコード={}", groupCode);
            }
            return result;
        } catch (SQLException e) {
            logger.error("グループマスタ情報取得中にエラーが発生しました: グループコード={}", groupCode, e);
            throw new RuntimeException("グループマスタ情報取得に失敗しました", e);
        }
    }
}
