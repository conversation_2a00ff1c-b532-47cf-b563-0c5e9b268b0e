package com.ms.bp.infrastructure.repository.dao;


import com.ms.bp.shared.common.db.JdbcTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

/**
 * システム管理者マスタデータアクセス実装
 */
public class SystemAdminDataAccess {
    private static final Logger logger = LoggerFactory.getLogger(SystemAdminDataAccess.class);

    private final JdbcTemplate jdbcTemplate;

    public SystemAdminDataAccess(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 指定社員がシステム管理者かどうかを確認
     *
     * @param shainCode 社員コード
     * @param systemOperationCompanyCode システム運用企業コード
     * @return システム管理者の場合true
     * @throws SQLException データベースアクセスエラー
     */
    public boolean isValidSystemAdmin(String shainCode, String systemOperationCompanyCode) throws SQLException {
        // 存在確認のため、COUNTクエリを実行
        var sql = """
            SELECT COUNT(1) as admin_count
            FROM M_SYSTM_KANRISHA
            WHERE SHAIN_CODE = ?
            AND SYSTM_UNYO_KIGYO_CODE = ?
            AND KYOKA_FLAG = '0'
            """;

        logger.debug("システム管理者確認SQL実行: shainCode={}, systemOperationCompanyCode={}", 
                    shainCode, systemOperationCompanyCode);

        var params = new Object[]{shainCode, systemOperationCompanyCode};

        var results = jdbcTemplate.query(sql, params, (rs) -> rs.getInt("admin_count"));
        
        var adminCount = results.isEmpty() ? 0 : results.getFirst();

        return adminCount > 0;
    }
}