package com.ms.bp.domain.master.repository;

import com.ms.bp.domain.master.model.GroupMasterInfo;
import java.util.Optional;

/**
 * グループマスタリポジトリ
 * グループマスタ領域のデータ永続化を抽象化
 * M_GROUPMST（グループマスタ）に対応
 * UserXXX命名規則に従い、英文術語との一貫性を保持
 */
public interface GroupMasterRepository {

    /**
     * グループコードでグループマスタの存在チェック
     * 指定されたグループコードがグループマスタに存在するかを確認する
     *
     * @param groupCode グループコード（4桁）
     * @return 存在する場合true、存在しない場合false
     */
    boolean existsByGroupCode(String groupCode);

    /**
     * グループコードでグループマスタ情報を取得
     * 指定されたグループコードに対応するグループ情報を取得する
     *
     * ビジネスルール：
     * - 有効期間内（終了日 >= システム日付）のレコードのみ取得
     * - 使用禁止区分が'0'のレコードのみ取得
     * - 関連するユニットマスタも有効である必要がある
     * - 複数件取得できた場合は1件目を使用
     *
     * @param groupCode グループコード（4桁）
     * @return グループマスタ情報（該当データがない場合は空のOptional）
     */
    Optional<GroupMasterInfo> findByGroupCode(String groupCode);
}
