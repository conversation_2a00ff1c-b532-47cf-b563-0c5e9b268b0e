package com.ms.bp.interfaces.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.common.exception.ValidationException;
import com.ms.bp.shared.util.MessageCodeUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * インポートリクエストDTO
 * インターフェース層のデータ転送オブジェクト
 */
@Data
public class ImportRequest {
    private String fileName;
    private String s3Key;
    private String dataType;
    /**
     * エリアリスト   画面選択されたエリアのエリアコードリスト
     *              採算管理単位計画策定エリアが選択された場合は"SKSA"
     *              複数のエリアを指定可能
     */
    private List<String> area;

    // ==================== 権限パラメータ ====================

    /**
     * ユニットコード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String unitCode;

    /**
     * 役職区分コード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String positionCode;

    /**
     * エリアコード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String areaCode;

    /**
     * グループコード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String groupCode;

    /**
     * システム管理者フラグ（権限判定用）
     * "1":システム管理者、その他:一般ユーザー
     */
    private String systemAdminFlag;

    /**
     * エリアリストをカンマ区切り文字列として取得
     * @return カンマ区切りのエリア文字列
     */
    @JsonIgnore
    public String getAreaString() {
        if (area == null || area.isEmpty()) {
            return null;
        }
        return String.join(",", area);
    }

    /**
     * エクスポートリクエストの検証を実行
     * エラーがある場合は即座にServiceExceptionをスロー
     *
     * @throws ValidationException 検証エラーがある場合
     */
    public void validate() throws ValidationException {
        List<String> errors = new ArrayList<>();
        // 1. 基本必須チェック：ファイル種別（dataType）は常に必須
        if (dataType == null || dataType.trim().isEmpty()) {
            errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "ファイル種別"));
        }

        // 2. 基本必須チェック：s3Keyは常に必須
        if (s3Key == null || s3Key.trim().isEmpty()) {
            errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "ファイル名"));
        }

        // 3. 基本必須チェック：エリア（area）は常に必須
        if (BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE.equals(dataType) || BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE.equals(dataType)) {
            if (area == null || area.isEmpty() || area.stream().allMatch(s -> s == null || s.trim().isEmpty())) {
                errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "エリア"));
            }
        }

        // 4. 基本必須チェック：fileNameは常に必須
        if (fileName == null || fileName.trim().isEmpty()) {
            errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "ファイル名"));
        }

        // エラーがある場合は例外をスロー
        if (!errors.isEmpty()) {
            throw new ValidationException(GlobalMessageConstants.ERR_019.getCode(),String.join("\n", errors));
        }
    }
}
