package com.ms.bp.interfaces.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ms.bp.shared.common.exception.ValidationException;
import lombok.Data;
import java.util.List;
import java.util.ArrayList;
import com.ms.bp.shared.common.constants.GlobalMessageConstants;
import com.ms.bp.shared.util.MessageCodeUtil;

import com.ms.bp.shared.common.constants.BusinessConstants;
import org.apache.commons.lang3.StringUtils;

/**
 * エクスポートリクエストDTO
 */
@Data
public class ExportRequest {

    /** データタイプ（1:次年度計画マスタ, 2:本社, 3:エリア, 4:間接利益計画） */
    private String dataType;

    /** エリアリスト */
    private List<String> area;

    /** 本社場所区分（0:本社, 1:場所, 2:本社・場所） */
    private String hnshBashoKubun;

    /** データ区分リスト（0:移管前, 1:移管後） */
    private List<String> dataKubun;

    /** カテゴリー区分 */
    private String ctgryKubun;

    // ==================== 権限パラメータ ====================

    /**
     * ユニットコード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String unitCode;

    /**
     * 役職区分コード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String positionCode;

    /**
     * エリアコード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String areaCode;

    /**
     * グループコード（権限判定用）
     * HTTPリクエストから取得、nullの場合は動的取得
     */
    private String groupCode;

    /**
     * システム管理者フラグ（権限判定用）
     * "1":システム管理者、その他:一般ユーザー
     */
    private String systemAdminFlag;

    @JsonIgnore
    public String getAreaString() {
        return (area == null || area.isEmpty()) ? "" : String.join(",", area);
    }

    @JsonIgnore
    public String getDataKubunString() {
        return (dataKubun == null || dataKubun.isEmpty()) ? "" : String.join(",", dataKubun);
    }

    /**
     * エクスポートリクエストの検証
     * @throws ValidationException 検証エラーがある場合
     */
    public void validate() throws ValidationException {
        List<String> errors = new ArrayList<>();

        // 基本必須項目チェック
        if (StringUtils.isEmpty(dataType)) {
            errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "ファイル種別"));
        }

        if (area == null || area.isEmpty() || area.stream().allMatch(StringUtils::isBlank)) {
            errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "エリア"));
        }

        // ファイル種別「2」または　ファイル種別「3」の場合の追加チェック
        if (BusinessConstants.FILE_TYPE_BUDGET_HONSHA_CODE.equals(dataType) || BusinessConstants.FILE_TYPE_BUDGET_AREA_CODE.equals(dataType)) {
            if (dataKubun == null || dataKubun.isEmpty() || dataKubun.stream().allMatch(StringUtils::isBlank)) {
                errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "データ区分"));
            }
        }

        // ファイル種別「4」の場合の追加チェック
        if (BusinessConstants.FILE_TYPE_INDIRECT_PROFIT_CODE.equals(dataType)) {
            if (StringUtils.isEmpty(ctgryKubun)) {
                errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "カテゴリー"));
            }
            if (StringUtils.isEmpty(hnshBashoKubun)) {
                errors.add(MessageCodeUtil.formatMessage(GlobalMessageConstants.ERR_019, "本社・場所区分"));
            }
        }

        // エラーがある場合は例外をスロー
        if (!errors.isEmpty()) {
            throw new ValidationException(GlobalMessageConstants.ERR_019.getCode(),String.join("\n", errors));
        }
    }
}