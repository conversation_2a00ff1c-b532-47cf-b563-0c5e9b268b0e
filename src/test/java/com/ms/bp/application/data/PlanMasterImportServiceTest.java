package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.domain.file.base.AbstractDataService;
import com.ms.bp.domain.file.base.AbstractImportService;
import com.ms.bp.domain.file.model.AnnounceMessage;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.shared.common.db.AuroraConnectionManager;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.common.io.facade.DataImportExportFacade;
import com.ms.bp.shared.util.DateUtil;
import com.ms.bp.util.TestDataManager;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ms.bp.util.CsvContentComparator;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.io.*;
import java.util.stream.Stream;

import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.http.AbortableInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

public class PlanMasterImportServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterImportServiceTest.class);
    private DataApplicationService dataApplicationService;
    // Mock オブジェクト
    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;
    @Mock
    private S3Service mockS3Service;
    @Mock
    private Context mockLambdaContext;
    private UserInfo testUserInfo;
    private ImportRequest testImportRequest;
    // エクスポート用Excel テストデータ管理器
    private TestDataManager testDataManager;
    // 挿入されたテストデータの追跡情報
    private Map<String, List<Map<String, Object>>> insertedDataTracker;
    private final String prefix_folder = "test-import-output-planmaster_";
    private final String prefix_file = "error_次年度計画マスタ";

    /**
     * ケース毎実施前作業
     */
    @BeforeEach
    @SuppressWarnings("unchecked")
    void setUp() {
        logger.info("=== importTask集成テストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");
            System.setProperty("WORKER_FUNCTION_NAME", "test-worker-function");

            // テスト対象
            dataApplicationService = new DataApplicationService();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // S3Serviceもモックに置き換え（FileProcessingServiceとTaskOrchestrationService内のS3Service）
            // DataApplicationService内のTaskOrchestrationServiceのS3Serviceを置き換える必要がある
            Field taskOrchestrationServiceField = DataApplicationService.class.getDeclaredField("taskOrchestrationService");
            taskOrchestrationServiceField.setAccessible(true);
            Object taskOrchestrationService = taskOrchestrationServiceField.get(dataApplicationService);

            // TaskOrchestrationService内のFileImportOrchestratorを取得
            Field fileImportOrchestratorField = taskOrchestrationService.getClass().getDeclaredField("fileImportOrchestrator");
            fileImportOrchestratorField.setAccessible(true);
            Object fileImportOrchestrator = fileImportOrchestratorField.get(taskOrchestrationService);

            // FileImportOrchestrator内のFileProcessingServiceを取得
            Field fileProcessingServiceField = fileImportOrchestrator.getClass().getDeclaredField("fileProcessingService");
            fileProcessingServiceField.setAccessible(true);
            Object fileProcessingService = fileProcessingServiceField.get(fileImportOrchestrator);

            // FileProcessingService内のS3Serviceを置き換え
            Field s3ServiceField = fileProcessingService.getClass().getDeclaredField("s3Service");
            s3ServiceField.setAccessible(true);
            s3ServiceField.set(fileProcessingService, mockS3Service);

            // ImportService内のDataImportExportFacadeも置き换える必要がある
            // FileImportOrchestrator内のImportServiceを取得
            Field importServiceField = fileImportOrchestrator.getClass().getDeclaredField("importServices");
            importServiceField.setAccessible(true);
            Map<String, AbstractImportService<?>> servicesMap =
                    (Map<String, AbstractImportService<?>>) importServiceField.get(fileImportOrchestrator);
            AbstractImportService<?> targetService = servicesMap.get(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);

            Field facadeField = null;
            Class<?> currentClass = targetService.getClass();
            while (currentClass != null) {
                try {
                    facadeField = currentClass.getDeclaredField("facade");
                    break;
                } catch (NoSuchFieldException e) {
                    currentClass = currentClass.getSuperclass();
                }
            }

            assert facadeField != null;
            facadeField.setAccessible(true);
            DataImportExportFacade mockFacade = new DataImportExportFacade(mockLambdaContext, mockS3Service);
            facadeField.set(targetService, mockFacade);

            // Mock の基本設定
            setupMockBehaviors();

            String insertDataPath = "planmasterimport/insertdata/planmaster_import_insert_test_data_1.xlsx";
            testDataManager = new TestDataManager(insertDataPath);
            insertedDataTracker = testDataManager.insertAllTestData();

            logger.info("=== importTask集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("テストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("テストセットアップに失敗しました", e);
        }

        // テスト用ユーザー情報を作成
        testUserInfo = createTestUserInfo();

        // テスト用インポートリクエストを作成
        testImportRequest = createTestImportRequest();
    }

    /**
     * ケース毎実施後作業
     */
    @AfterEach
    void tearDown() {
        logger.info("=== 集成テストクリーンアップ開始 ===");

        // 挿入したテストデータを削除
        if (insertedDataTracker != null) {
            testDataManager.deleteAllTestData(insertedDataTracker);
        }

        logger.info("=== 集成テストクリーンアップ完了 ===");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：パータン1: ヘッダ数不足
     * パータン2: ヘッダ名不正
     * 確認内容：下記異常が発生
     * ERR_010: アップロードされたファイルは{0}の形式ではありません。内容をご確認ください。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "ファイル[{0}]のヘッダ不正テスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "planmasterimport/uploaddata/planmaster_import_test_data_1.csv",// ヘッダ数不足
            "planmasterimport/uploaddata/planmaster_import_test_data_2.csv" // ヘッダ名不正
    })
    @Order(1)
    void testExecuteImportTask_ヘッダチェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);
        // 期待エラー内容
        List<String> expectContents = List.of(
                "アップロードされたファイルは次年度計画マスタの形式ではありません。内容をご確認ください。"
        );

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：ファイル内のエリアCDまたは移管先エリアCDが一致しない
     * data1:エリアコード：権限エリアと一致しない、移管先エリアコード：権限エリアと一致しない
     * data2:エリアコード：権限エリアと一致しない、移管先エリアコード：なし
     * data3:エリアコード：なし、移管先エリアコード：権限エリアと一致しない
     * 確認内容：下記異常が発生
     * ERR_027: 行：{0}、項目：{1}、エラー内容：登録権限のない値が入力されています。
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(2)
    void testExecuteImportTask_権限チェック_異常系() {
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_3.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testUserInfo.setAreaCode("0001");
        setupMockS3(uploadFilePath);

        // 期待エラー内容
        List<String> expectContents = List.of(
                "行：1、項目：エリアCD、エラー内容：登録権限のない値が入力されています。",
                "行：1、項目：移管先エリアCD、エラー内容：登録権限のない値が入力されています。",
                "行：2、項目：エリアCD、エラー内容：登録権限のない値が入力されています。",
                "行：3、項目：エリアCD、エラー内容：必須チェックエラー",
                "行：3、項目：移管先エリアCD、エラー内容：登録権限のない値が入力されています。"
        );

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：下記必須項目を設定しない
     * ・エリアCD
     * ・カテCD
     * ・グループ
     * ・ユニット
     * ・企業CD
     * ・業態集計
     * ・採算管理単位CD
     * ・変更後取組区分
     * 確認内容：下記異常が発生
     * ERR_016: 行：{0}、項目：{1}、エラー内容：必須チェックエラー
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(3)
    void testExecuteImportTask_必須チェック_異常系() {
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_4.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);

        // 期待エラー内容
        List<String> expectContents = List.of(
                "行：1、項目：エリアCD、エラー内容：必須チェックエラー",
                "行：1、項目：カテCD、エラー内容：必須チェックエラー",
                "行：1、項目：グループ、エラー内容：必須チェックエラー",
                "行：1、項目：ユニット、エラー内容：必須チェックエラー",
                "行：1、項目：企業CD、エラー内容：必須チェックエラー",
                "行：1、項目：業態集計、エラー内容：必須チェックエラー",
                "行：1、項目：採算管理単位CD、エラー内容：必須チェックエラー",
                "行：1、項目：変更後取組区分、エラー内容：必須チェックエラー"
        );

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：下記項目を桁数不正に設定
     * ・エリアCD: 最大が4 最小が4
     * ・カテCD: 最大が4 最小が4
     * ・グループ: 最大が4 最小が4
     * ・ユニット: 最大が5 最小が5
     * ・担当者: 最大が1 最小が20
     * ・企業CD: 最大が7 最小が7
     * ・業態集計: 最大が2 最小が2
     * ・業態名: 最大が1 最小が30
     * ・採算管理単位CD: 最大が7 最小が7
     * ・採算管理単位名: 最大が1 最小が25
     * ・変更後取組区分: 最大が1 最小が20
     * ・移管先エリアCD: 最大が4 最小が4
     * ・移管先グループCD: 最大が4 最小が4
     * ・移管先ユニットCD: 最大が5 最小が5
     * 確認内容：下記異常が発生
     * 桁数エラー:
     * ERR_017: 行：{0}、項目：{1}、エラー内容：桁数エラー。{2}桁から{3}桁の間で入力してください。
     * コード存在チェック
     * ERR_022: 行：{0}、項目：{1}、エラー内容：項目エラー。{2}は存在しないコードです。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "ファイル[{0}]の桁数不正テスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "planmasterimport/uploaddata/planmaster_import_test_data_5.csv",// 桁数未満
            "planmasterimport/uploaddata/planmaster_import_test_data_6.csv" // 桁数超える
    })
    @Order(4)
    void testExecuteImportTask_桁数チェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);
        // 期待エラー内容
        List<String> expectContents = null;
        if (uploadFilePath.contains("5")) {
            testUserInfo.setAreaCode("700");
            expectContents = List.of(
                    "行：1、項目：エリアCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：カテCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：グループ、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：ユニット、エラー内容：桁数エラー。5桁から5桁の間で入力してください。",
                    "行：1、項目：企業CD、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：業態集計、エラー内容：桁数エラー。2桁から2桁の間で入力してください。",
                    "行：1、項目：採算管理単位CD、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：移管先エリアCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：移管先グループCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：移管先ユニットCD、エラー内容：桁数エラー。5桁から5桁の間で入力してください。",
                    "行：1、項目：移管先エリアCD、エラー内容：項目エラー。CSV.移管先エリアCDは存在しないコードです。",
                    "行：1、項目：移管先グループCD、エラー内容：項目エラー。CSV.移管先グループCDは存在しないコードです。",
                    "行：1、項目：移管先ユニットCD、エラー内容：項目エラー。CSV.移管先ユニットCDは存在しないコードです。"
            );
        } else {
            testUserInfo.setAreaCode("70001");
            expectContents = List.of(
                    "行：1、項目：エリアCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：カテCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：グループ、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：ユニット、エラー内容：桁数エラー。5桁から5桁の間で入力してください。",
                    "行：1、項目：担当者、エラー内容：桁数エラー。1桁から20桁の間で入力してください。",
                    "行：1、項目：企業CD、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：業態集計、エラー内容：桁数エラー。2桁から2桁の間で入力してください。",
                    "行：1、項目：業態名、エラー内容：桁数エラー。1桁から30桁の間で入力してください。",
                    "行：1、項目：採算管理単位CD、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                    "行：1、項目：採算管理単位名、エラー内容：桁数エラー。1桁から25桁の間で入力してください。",
                    "行：1、項目：変更後取組区分、エラー内容：桁数エラー。1桁から20桁の間で入力してください。",
                    "行：1、項目：移管先エリアCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：移管先グループCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                    "行：1、項目：移管先ユニットCD、エラー内容：桁数エラー。5桁から5桁の間で入力してください。",
                    "行：1、項目：移管先エリアCD、エラー内容：項目エラー。CSV.移管先エリアCDは存在しないコードです。",
                    "行：1、項目：移管先グループCD、エラー内容：項目エラー。CSV.移管先グループCDは存在しないコードです。",
                    "行：1、項目：移管先ユニットCD、エラー内容：項目エラー。CSV.移管先ユニットCDは存在しないコードです。"
            );
        }

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：下記項目を不正に設定
     * ・移管先エリアCD
     * ・移管先グループCD
     * ・移管先ユニットCD
     * 確認内容：下記異常が発生
     * コード存在チェック
     * ERR_022: 行：{0}、項目：{1}、エラー内容：項目エラー。{2}は存在しないコードです。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "ファイル[{0}]の移管先CDの設定不正テスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "planmasterimport/uploaddata/planmaster_import_test_data_15.csv", // <開始日
            "planmasterimport/uploaddata/planmaster_import_test_data_16.csv", // >終了日
            "planmasterimport/uploaddata/planmaster_import_test_data_17.csv", // 使用禁止区分=1
    })
    @Order(5)
    void testExecuteImportTask_コード存在チェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);

        // 期待エラー内容
        List<String> expectContents = List.of(
                "行：1、項目：移管先エリアCD、エラー内容：項目エラー。CSV.移管先エリアCDは存在しないコードです。",
                "行：1、項目：移管先グループCD、エラー内容：項目エラー。CSV.移管先グループCDは存在しないコードです。",
                "行：1、項目：移管先ユニットCD、エラー内容：項目エラー。CSV.移管先ユニットCDは存在しないコードです。"
        );
        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：下記項目を正しく設定しない
     * ・エリアCD: 半角数字
     * ・カテCD: 半角数字
     * ・グループ: 半角数字
     * ・ユニット: 半角数字
     * ・企業CD: 半角数字
     * ・業態集計: 半角数字
     * ・採算管理単位CD: 半角英数字
     * ・移管先エリアCD: 半角数字
     * ・移管先グループCD: 半角数字
     * ・移管先ユニットCD: 半角数字
     * 確認内容：下記異常が発生
     * ERR_018: 行：{0}、項目：{1}、エラー内容：書式エラー。{2}で入力してください。
     * 履歴のステータス: 失敗
     */
    @ParameterizedTest(name = "ファイル[{0}]の書式不正テスト") // テスト名にパラメータを表示
    @ValueSource(strings = {
            "planmasterimport/uploaddata/planmaster_import_test_data_7.csv",// 半角英字・採算管理単位CDが全角英字
            "planmasterimport/uploaddata/planmaster_import_test_data_8.csv", // 全角数字
            "planmasterimport/uploaddata/planmaster_import_test_data_9.csv", // 記号(!@#$%^&*_)
    })
    @Order(6)
    void testExecuteImportTask_書式チェック_異常系(String uploadFilePath) {
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);
        if (uploadFilePath.contains("7")) {
            testUserInfo.setAreaCode("aaaa");
        } else if (uploadFilePath.contains("8")) {
            testUserInfo.setAreaCode("２２２２");
        } else {
            testUserInfo.setAreaCode("~!@#");
        }

        // 期待エラー内容
        List<String> expectContents = List.of(
                "行：1、項目：エリアCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：カテCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：グループ、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：ユニット、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：企業CD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：業態集計、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：採算管理単位CD、エラー内容：書式エラー。半角英数字で入力してください。",
                "行：1、項目：移管先エリアCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：移管先グループCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：移管先ユニットCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：1、項目：移管先エリアCD、エラー内容：項目エラー。CSV.移管先エリアCDは存在しないコードです。",
                "行：1、項目：移管先グループCD、エラー内容：項目エラー。CSV.移管先グループCDは存在しないコードです。",
                "行：1、項目：移管先ユニットCD、エラー内容：項目エラー。CSV.移管先ユニットCDは存在しないコードです。"
        );

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：複数行に対していろいろな不正な設定
     * 確認内容：下記異常が発生
     * ERR_016: 行：{0}、項目：{1}、エラー内容：必須チェックエラー
     * ERR_017: 行：{0}、項目：{1}、エラー内容：桁数エラー。{2}桁から{3}桁の間で入力してください。
     * ERR_018: 行：{0}、項目：{1}、エラー内容：書式エラー。{2}で入力してください。
     * ERR_022: 行：{0}、項目：{1}、エラー内容：項目エラー。{2}は存在しないコードです。
     * ERR_027: 行：{0}、項目：{1}、エラー内容：登録権限のない値が入力されています。
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(7)
    void testExecuteImportTask_複数行チェック_異常系() {
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_10.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);
        testUserInfo.setAreaCode("２２２２");

        // 期待エラー内容
        List<String> expectContents = List.of(
                "行：1、項目：エリアCD、エラー内容：必須チェックエラー",
                "行：1、項目：カテCD、エラー内容：必須チェックエラー",
                "行：1、項目：グループ、エラー内容：必須チェックエラー",
                "行：1、項目：ユニット、エラー内容：必須チェックエラー",
                "行：1、項目：企業CD、エラー内容：必須チェックエラー",
                "行：1、項目：業態集計、エラー内容：必須チェックエラー",
                "行：1、項目：採算管理単位CD、エラー内容：必須チェックエラー",
                "行：1、項目：変更後取組区分、エラー内容：必須チェックエラー",
                "行：1、項目：移管先エリアCD、エラー内容：登録権限のない値が入力されています。",
                "行：2、項目：エリアCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：カテCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：グループ、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：ユニット、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：企業CD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：業態集計、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：採算管理単位CD、エラー内容：書式エラー。半角英数字で入力してください。",
                "行：2、項目：移管先エリアCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：移管先グループCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：移管先ユニットCD、エラー内容：書式エラー。半角数字で入力してください。",
                "行：2、項目：移管先エリアCD、エラー内容：項目エラー。CSV.移管先エリアCDは存在しないコードです。",
                "行：2、項目：移管先グループCD、エラー内容：項目エラー。CSV.移管先グループCDは存在しないコードです。",
                "行：2、項目：移管先ユニットCD、エラー内容：項目エラー。CSV.移管先ユニットCDは存在しないコードです。",
                "行：3、項目：エリアCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                "行：3、項目：カテCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                "行：3、項目：グループ、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                "行：3、項目：ユニット、エラー内容：桁数エラー。5桁から5桁の間で入力してください。",
                "行：3、項目：企業CD、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                "行：3、項目：業態集計、エラー内容：桁数エラー。2桁から2桁の間で入力してください。",
                "行：3、項目：採算管理単位CD、エラー内容：桁数エラー。7桁から7桁の間で入力してください。",
                "行：3、項目：移管先エリアCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                "行：3、項目：移管先グループCD、エラー内容：桁数エラー。4桁から4桁の間で入力してください。",
                "行：3、項目：移管先ユニットCD、エラー内容：桁数エラー。5桁から5桁の間で入力してください。",
                "行：3、項目：エリアCD、エラー内容：登録権限のない値が入力されています。",
                "行：3、項目：移管先エリアCD、エラー内容：登録権限のない値が入力されています。",
                "行：3、項目：移管先エリアCD、エラー内容：項目エラー。CSV.移管先エリアCDは存在しないコードです。",
                "行：3、項目：移管先グループCD、エラー内容：項目エラー。CSV.移管先グループCDは存在しないコードです。",
                "行：3、項目：移管先ユニットCD、エラー内容：項目エラー。CSV.移管先ユニットCDは存在しないコードです。"
        );

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertEquals(expectContents, actualContents, "両方のエラー内容が不一致する");
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：複数行に対していろいろな不正な設定
     * 且、100以上のチェックエラーがある
     * 確認内容：下記異常が発生
     * エラーファイルに100件のみエラーがある
     * 履歴のステータス: 失敗
     */
    @Test
    @Order(8)
    void testExecuteImportTask_100件エラー以上_異常系() {
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_11.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);
        testUserInfo.setAreaCode("２２２２");

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_FAILED_CODE);

        // 実施結果エラー内容
        List<String> actualContents = CsvContentComparator.findTxtFileAndGetContent(prefix_folder, prefix_file);
        // 両方比較
        assertThat(actualContents).hasSize(100);
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：csvに記号「"」を単独で設定
     * 確認内容：履歴のステータス: システムエラー
     */
    @Test
    @Order(9)
    void testExecuteImportTask_システムエラー_異常系() {
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_12.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        setupMockS3(uploadFilePath);
        testUserInfo.setAreaCode("２２２２");

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE);
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：複数件登録(全項目)
     * data1:新規、エリアコード：権限エリアと一致、移管先エリアコード：権限エリアと一致
     * data2:新規、エリアコード：権限エリアと一致、移管先エリアコード：権限エリアと一致しない
     * 確認内容：新規データがアップロードファイルの内容と一致する
     * 履歴のステータス: 完了
     */
    @Test
    @Order(10)
    void testExecuteImportTask_新規追加_正常系() {
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_13.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testUserInfo.setAreaCode("9800");
        setupMockS3(uploadFilePath);
        DateUtil.setTestFixedDateTime("9999");

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

        // 新規データ検証
        List<JinendoKkk> results = getDatabaseData("9999", List.of("9999901", "9999902"));
        assertThat(results).hasSize(2);
        assertEquals("9999", results.get(0).nendo());
        assertEquals("9999901", results.get(0).ssnknTncd());
        assertEquals("2000", results.get(0).groupCode());
        assertEquals("9800", results.get(0).areaCode());
        assertEquals("30000", results.get(0).unitCode());
        assertEquals("1000", results.get(0).ctgryCode());
        assertEquals("4000000", results.get(0).kigyoCode());
        assertEquals("50", results.get(0).gytShkNo());
        assertEquals("s001", results.get(0).subCtgryCode());
        assertEquals("900001", results.get(0).systmUnyoKigyoCode());
        assertEquals("t001", results.get(0).tntshMei());
        assertEquals("dummy_01", results.get(0).gytmKanji());
        assertEquals("001", results.get(0).hnkgTrkmKubun());
        assertEquals("9800", results.get(0).iknskAreaCode());
        assertEquals("9908", results.get(0).iknskGroupCode());
        assertEquals("99990", results.get(0).iknskUnitCode());
        assertEquals("dummy_test001", results.get(0).ssnKanriTnmKanji());
        assertEquals("9800", results.get(0).tkyAreaCode());
        assertEquals("9908", results.get(0).tkyGroupCode());
        assertEquals("99990", results.get(0).tkyUnitCode());
        assertEquals("BAT_005   ", results.get(0).trkPrgrmId());
        assertEquals("900001", results.get(0).trkSystmUnyoKigyoCode());
        assertEquals("JNED01", results.get(0).trkShainCode());
        assertEquals("BAT_005   ", results.get(0).kshnPrgrmId());
        assertEquals("900001", results.get(0).kshnSystmUnyoKigyoCode());
        assertEquals("JNED01", results.get(0).kshnShainCode());
        assertEquals("1", results.get(0).vrsn());
        assertNotNull(results.get(0).rcrdTrkNchj());
        assertNotNull(results.get(0).rcrdKshnNchj());

        assertEquals("9999", results.get(1).nendo());
        assertEquals("9999902", results.get(1).ssnknTncd());
        assertEquals("2001", results.get(1).groupCode());
        assertEquals("9800", results.get(1).areaCode());
        assertEquals("30001", results.get(1).unitCode());
        assertEquals("1001", results.get(1).ctgryCode());
        assertEquals("4000001", results.get(1).kigyoCode());
        assertEquals("51", results.get(1).gytShkNo());
        assertEquals("s002", results.get(1).subCtgryCode());
        assertEquals("900001", results.get(1).systmUnyoKigyoCode());
        assertEquals("t002", results.get(1).tntshMei());
        assertEquals("dummy_02", results.get(1).gytmKanji());
        assertEquals("002", results.get(1).hnkgTrkmKubun());
        assertEquals("9818", results.get(1).iknskAreaCode());
        assertEquals("9909", results.get(1).iknskGroupCode());
        assertEquals("99991", results.get(1).iknskUnitCode());
        assertEquals("dummy_test002", results.get(1).ssnKanriTnmKanji());
        assertEquals("9818", results.get(1).tkyAreaCode());
        assertEquals("9909", results.get(1).tkyGroupCode());
        assertEquals("99991", results.get(1).tkyUnitCode());
        assertEquals("BAT_005   ", results.get(1).trkPrgrmId());
        assertEquals("900001", results.get(1).trkSystmUnyoKigyoCode());
        assertEquals("JNED01", results.get(1).trkShainCode());
        assertEquals("BAT_005   ", results.get(1).kshnPrgrmId());
        assertEquals("900001", results.get(1).kshnSystmUnyoKigyoCode());
        assertEquals("JNED01", results.get(1).kshnShainCode());
        assertEquals("1", results.get(1).vrsn());
        assertNotNull(results.get(1).rcrdTrkNchj());
        assertNotNull(results.get(1).rcrdKshnNchj());

        // 新規追加データを削除しなくて、後続の更新ケースで利用必要
    }

    /**
     * 試験対象：次年度計画マスタ_インポート
     * 試験条件：複数件更新(全項目)、1件新規(必須項目だけ)
     * data1:更新、エリアコード：権限エリアと一致しない、移管先エリアコード：権限エリアと一致
     * data2:更新、エリアコード：権限エリアと一致、移管先エリアコード：権限エリアと一致しない
     * data3:新規、エリアコード：権限エリアと一致、移管先エリアコード：なし
     * 確認内容：新規データがアップロードファイルの内容と一致する
     * 履歴のステータス: 完了
     */
    @Test
    @Order(11)
    void testExecuteImportTask_更新_新規_正常系() {
        // testExecuteImportTask_新規追加_正常系の実施依頼要
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_14.csv";
        testImportRequest.setS3Key(uploadFilePath);
        testImportRequest.setFileName(uploadFilePath);
        testUserInfo.setAreaCode("9800");
        testUserInfo.setShainCode("JNED11");
        testUserInfo.setSystemOperationCompanyCode("900011");
        setupMockS3(uploadFilePath);
        DateUtil.setTestFixedDateTime("9999");
        try {

            // 既存データ取得
            List<JinendoKkk> bfUpdateDatas = getDatabaseData("9999", List.of("9999901", "9999902"));

            // 実行と実行前後の履歴確認
            doExecute(BusinessConstants.BATCH_STATUS_COMPLETED_CODE);

            // 更新データ検証
            List<JinendoKkk> results = getDatabaseData("9999", List.of("9999901", "9999902", "9999903"));
            assertThat(results).hasSize(3);
            assertEquals("9999", results.get(0).nendo());
            assertEquals("9999901", results.get(0).ssnknTncd());
            assertEquals("2000", results.get(0).groupCode());
            assertEquals("9819", results.get(0).areaCode());
            assertEquals("30010", results.get(0).unitCode());
            assertEquals("1010", results.get(0).ctgryCode());
            assertEquals("4000010", results.get(0).kigyoCode());
            assertEquals("60", results.get(0).gytShkNo());
            assertEquals("s011", results.get(0).subCtgryCode());
            assertEquals("900011", results.get(0).systmUnyoKigyoCode());
            assertEquals("t011", results.get(0).tntshMei());
            assertEquals("dummy_11", results.get(0).gytmKanji());
            assertEquals("011", results.get(0).hnkgTrkmKubun());
            assertEquals("9800", results.get(0).iknskAreaCode());
            assertEquals("9918", results.get(0).iknskGroupCode());
            assertEquals("99992", results.get(0).iknskUnitCode());
            assertEquals("dummy_test011", results.get(0).ssnKanriTnmKanji());
            assertEquals("9800", results.get(0).tkyAreaCode());
            assertEquals("9918", results.get(0).tkyGroupCode());
            assertEquals("99992", results.get(0).tkyUnitCode());
            assertEquals("BAT_005   ", results.get(0).trkPrgrmId());
            assertEquals("900001", results.get(0).trkSystmUnyoKigyoCode());
            assertEquals("JNED01", results.get(0).trkShainCode());
            assertEquals("BAT_005   ", results.get(0).kshnPrgrmId());
            assertEquals("900011", results.get(0).kshnSystmUnyoKigyoCode());
            assertEquals("JNED11", results.get(0).kshnShainCode());
            assertEquals("1", results.get(0).vrsn());
            assertNotNull(results.get(0).rcrdTrkNchj());
            assertNotNull(results.get(0).rcrdKshnNchj());

            assertEquals("9999", results.get(1).nendo());
            assertEquals("9999902", results.get(1).ssnknTncd());
            assertEquals("2001", results.get(1).groupCode());
            assertEquals("9800", results.get(1).areaCode());
            assertEquals("30011", results.get(1).unitCode());
            assertEquals("1011", results.get(1).ctgryCode());
            assertEquals("4000011", results.get(1).kigyoCode());
            assertEquals("61", results.get(1).gytShkNo());
            assertEquals("s012", results.get(1).subCtgryCode());
            assertEquals("900011", results.get(1).systmUnyoKigyoCode());
            assertEquals("t012", results.get(1).tntshMei());
            assertEquals("dummy_12", results.get(1).gytmKanji());
            assertEquals("012", results.get(1).hnkgTrkmKubun());
            assertEquals("9819", results.get(1).iknskAreaCode());
            assertEquals("9919", results.get(1).iknskGroupCode());
            assertEquals("99993", results.get(1).iknskUnitCode());
            assertEquals("dummy_test012", results.get(1).ssnKanriTnmKanji());
            assertEquals("9819", results.get(1).tkyAreaCode());
            assertEquals("9919", results.get(1).tkyGroupCode());
            assertEquals("99993", results.get(1).tkyUnitCode());
            assertEquals("BAT_005   ", results.get(1).trkPrgrmId());
            assertEquals("900001", results.get(1).trkSystmUnyoKigyoCode());
            assertEquals("JNED01", results.get(1).trkShainCode());
            assertEquals("BAT_005   ", results.get(1).kshnPrgrmId());
            assertEquals("900011", results.get(1).kshnSystmUnyoKigyoCode());
            assertEquals("JNED11", results.get(1).kshnShainCode());
            assertEquals("1", results.get(1).vrsn());
            assertNotNull(results.get(1).rcrdTrkNchj());
            assertNotNull(results.get(1).rcrdKshnNchj());

            assertEquals("9999", results.get(2).nendo());
            assertEquals("9999903", results.get(2).ssnknTncd());
            assertEquals("2000", results.get(2).groupCode());
            assertEquals("9800", results.get(2).areaCode());
            assertEquals("30000", results.get(2).unitCode());
            assertEquals("1000", results.get(2).ctgryCode());
            assertEquals("4000000", results.get(2).kigyoCode());
            assertEquals("50", results.get(2).gytShkNo());
            assertEquals("    ", results.get(2).subCtgryCode());
            assertEquals("900011", results.get(2).systmUnyoKigyoCode());
            assertEquals("", results.get(2).tntshMei());
            assertEquals("", results.get(2).gytmKanji());
            assertEquals("001", results.get(2).hnkgTrkmKubun());
            assertEquals("", results.get(2).iknskAreaCode());
            assertEquals("", results.get(2).iknskGroupCode());
            assertEquals("", results.get(2).iknskUnitCode());
            assertEquals("", results.get(2).ssnKanriTnmKanji());
            assertEquals("9800", results.get(2).tkyAreaCode());
            assertEquals("2000", results.get(2).tkyGroupCode());
            assertEquals("30000", results.get(2).tkyUnitCode());
            assertEquals("BAT_005   ", results.get(2).trkPrgrmId());
            assertEquals("900011", results.get(2).trkSystmUnyoKigyoCode());
            assertEquals("JNED11", results.get(2).trkShainCode());
            assertEquals("BAT_005   ", results.get(2).kshnPrgrmId());
            assertEquals("900011", results.get(2).kshnSystmUnyoKigyoCode());
            assertEquals("JNED11", results.get(2).kshnShainCode());
            assertEquals("1", results.get(2).vrsn());
            assertNotNull(results.get(2).rcrdTrkNchj());
            assertNotNull(results.get(2).rcrdKshnNchj());

            assertEquals(results.get(0).nendo(), bfUpdateDatas.get(0).nendo());
            assertEquals(results.get(0).ssnknTncd(), bfUpdateDatas.get(0).ssnknTncd());
            assertEquals(results.get(0).groupCode(), bfUpdateDatas.get(0).groupCode());
            assertNotEquals(results.get(0).areaCode(), bfUpdateDatas.get(0).areaCode());
            assertNotEquals(results.get(0).unitCode(), bfUpdateDatas.get(0).unitCode());
            assertNotEquals(results.get(0).ctgryCode(), bfUpdateDatas.get(0).ctgryCode());
            assertNotEquals(results.get(0).kigyoCode(), bfUpdateDatas.get(0).kigyoCode());
            assertNotEquals(results.get(0).gytShkNo(), bfUpdateDatas.get(0).gytShkNo());
            assertNotEquals(results.get(0).subCtgryCode(), bfUpdateDatas.get(0).subCtgryCode());
            assertNotEquals(results.get(0).systmUnyoKigyoCode(), bfUpdateDatas.get(0).systmUnyoKigyoCode());
            assertNotEquals(results.get(0).tntshMei(), bfUpdateDatas.get(0).tntshMei());
            assertNotEquals(results.get(0).gytmKanji(), bfUpdateDatas.get(0).gytmKanji());
            assertNotEquals(results.get(0).hnkgTrkmKubun(), bfUpdateDatas.get(0).hnkgTrkmKubun());
            assertEquals(results.get(0).iknskAreaCode(), bfUpdateDatas.get(0).iknskAreaCode());
            assertNotEquals(results.get(0).iknskGroupCode(), bfUpdateDatas.get(0).iknskGroupCode());
            assertNotEquals(results.get(0).iknskUnitCode(), bfUpdateDatas.get(0).iknskUnitCode());
            assertNotEquals(results.get(0).ssnKanriTnmKanji(), bfUpdateDatas.get(0).ssnKanriTnmKanji());
            assertEquals(results.get(0).tkyAreaCode(), bfUpdateDatas.get(0).tkyAreaCode());
            assertNotEquals(results.get(0).tkyGroupCode(), bfUpdateDatas.get(0).tkyGroupCode());
            assertNotEquals(results.get(0).tkyUnitCode(), bfUpdateDatas.get(0).tkyUnitCode());
            assertEquals(results.get(0).trkPrgrmId(), bfUpdateDatas.get(0).trkPrgrmId());
            assertEquals(results.get(0).trkSystmUnyoKigyoCode(), bfUpdateDatas.get(0).trkSystmUnyoKigyoCode());
            assertEquals(results.get(0).trkShainCode(), bfUpdateDatas.get(0).trkShainCode());
            assertEquals(results.get(0).kshnPrgrmId(), bfUpdateDatas.get(0).kshnPrgrmId());
            assertNotEquals(results.get(0).kshnSystmUnyoKigyoCode(), bfUpdateDatas.get(0).kshnSystmUnyoKigyoCode());
            assertNotEquals(results.get(0).kshnShainCode(), bfUpdateDatas.get(0).kshnShainCode());
            assertEquals(results.get(0).vrsn(), bfUpdateDatas.get(0).vrsn());
            assertEquals(results.get(0).rcrdTrkNchj(), bfUpdateDatas.get(0).rcrdTrkNchj());
            assertNotEquals(results.get(0).rcrdKshnNchj(), bfUpdateDatas.get(0).rcrdKshnNchj());

            assertNotEquals(results.get(1).iknskAreaCode(), bfUpdateDatas.get(1).iknskAreaCode());
            assertNotEquals(results.get(1).tkyAreaCode(), bfUpdateDatas.get(1).tkyAreaCode());

        } catch (Exception e) {
            logger.error("更新_新規_正常系エラー: {}", e.getMessage(), e);
        } finally {
            // 新規と更新データ削除
            TestDataManager.deleteTableData("T_JINENDO_KKK", List.of(Map.of("NENDO", "9999"), Map.of("SSNKN_TNCD", "9999901"), Map.of("GROUP_CODE", "2000")));
            TestDataManager.deleteTableData("T_JINENDO_KKK", List.of(Map.of("NENDO", "9999"), Map.of("SSNKN_TNCD", "9999902"), Map.of("GROUP_CODE", "2001")));
            TestDataManager.deleteTableData("T_JINENDO_KKK", List.of(Map.of("NENDO", "9999"), Map.of("SSNKN_TNCD", "9999903"), Map.of("GROUP_CODE", "2001")));
        }
    }

    /**
     * Mock オブジェクトの基本動作を設定
     */
    private void setupMockBehaviors() {
        try {
            // AsyncLambdaInvoker の Mock 設定
            doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any(WorkerPayload.class));

            // S3Service の Mock 設定 - ファイルを本地保存するようにmock
            when(mockS3Service.uploadFileFromStream(any(InputStream.class), anyString(), anyString(), anyLong(), any()))
                    .thenAnswer(invocation -> {
                        InputStream inputStream = invocation.getArgument(0);
                        String s3Key = invocation.getArgument(1);

                        // S3キーからファイル名を抽出
                        String fileName = s3Key.substring(s3Key.lastIndexOf("/") + 1);

                        // テスト用のローカルディレクトリを作成
                        Path testDir = Files.createTempDirectory(prefix_folder);
                        if (!Files.exists(testDir)) {
                            Files.createDirectories(testDir);
                        }

                        // ローカルファイルパスを生成
                        Path localFilePath = testDir.resolve(fileName);

                        // InputStreamをローカルファイルに保存
                        try (OutputStream outputStream = Files.newOutputStream(localFilePath)) {
                            inputStream.transferTo(outputStream);
                        }

                        logger.info("ファイルをローカルに保存しました: {}", localFilePath.toAbsolutePath());

                        // S3アップロード成功の模擬レスポンスを返す
                        return Map.of(
                                "success", true,
                                "key", s3Key,
                                "localPath", localFilePath.toAbsolutePath().toString()
                        );
                    });

            // Lambda Context の Mock 設定
            when(mockLambdaContext.getRemainingTimeInMillis()).thenReturn(900000); // 15分（BackgroundTimeoutMonitorの緩衝時間10分より大きく設定）
            when(mockLambdaContext.getAwsRequestId()).thenReturn("test-request-id");

            logger.debug("Mock オブジェクトの基本動作設定完了");
        } catch (Exception e) {
            logger.error("Mock 設定エラー: {}", e.getMessage(), e);
            throw new RuntimeException("Mock 設定に失敗しました", e);
        }
    }

    /**
     * 動態的の S3Service の Mock 設定
     */
    private void setupMockS3(String filePath) {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("testdata/" + filePath);
            GetObjectResponse getObjectResponse = GetObjectResponse.builder().build();
            ResponseInputStream<GetObjectResponse> responseInputStream = new ResponseInputStream<>(
                    getObjectResponse,
                    AbortableInputStream.create(inputStream)
            );
            when(mockS3Service.getInputStreamFromS3Url(eq(filePath))).thenReturn(responseInputStream);
        } catch (IOException e) {
            logger.error("S3 Mock 設定エラー: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * ImportJobStatusを追加と内容検証と情報を取得
     * 状態表データの検証用
     */
    private ImportJobStatus addAndCheckAndGetImportJobInfo() {
        try {
            // 事前にjobを手動追加する
            var importJobResponse = dataApplicationService.startImport(testImportRequest,
                    testUserInfo, mockLambdaContext);
            Long rrkBango = Long.parseLong(importJobResponse.getJobId());
            // ジョブが正常に作成されたことを確認
            assertNotNull(importJobResponse.getJobId());
            assertEquals("ACCEPTED", importJobResponse.getStatus());

            // 初期状態の確認
            ImportJobStatus initObj = getImportJobStatusFromDatabase(rrkBango);
            assertNotNull(initObj);
            assertEquals(rrkBango, initObj.getRrkBango());
            assertEquals(testUserInfo.getSystemOperationCompanyCode(), initObj.getSystmUnyoKigyoCode());
            assertEquals(testUserInfo.getShainCode(), initObj.getShainCode());
            assertEquals(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE, initObj.getFileShbts());
            assertNull(initObj.getArea());
            assertTrue(initObj.getFileMei().startsWith("planmasterimport/uploaddata/"));
            assertNull(initObj.getErrorFileMei());
            assertNotNull(initObj.getUploadKshNchj());
            assertNull(initObj.getUploadKnrNchj());
            assertEquals(BusinessConstants.BATCH_STATUS_PROCESSING_CODE, initObj.getStts());
            assertEquals("BAT_005   ", initObj.getTrkPrgrmId());
            assertEquals("SYSTEM", initObj.getTrkSystmUnyoKigyoCode());
            assertEquals("SYSTEM", initObj.getTrkShainCode());
            assertEquals("BAT_005   ", initObj.getKshnPrgrmId());
            assertEquals("SYSTEM", initObj.getKshnSystmUnyoKigyoCode());
            assertEquals("SYSTEM", initObj.getKshnShainCode());
            assertEquals("1", initObj.getVrsn().toString());
            assertNotNull(initObj.getRcrdTrkNchj());
            assertNotNull(initObj.getRcrdKshnNchj());

            // 少し待機
            Thread.sleep(900);
            return initObj;
        } catch (Exception e) {
            System.err.println("エラーが発生: " + e.getMessage());
            e.printStackTrace();
            // データベース接続エラーなどの場合は、適切な例外処理が行われていることを確認
            assertInstanceOf(RuntimeException.class, e, "適切な例外タイプであること");
        }
        return null;
    }

    /**
     * 指定番号でステータスを比較する
     * 状態表データの検証用
     *
     * @param initObj 更新前履歴
     * @param status ステータス
     */
    private void checkImportJobInfo(ImportJobStatus initObj,String status) {
        ImportJobStatus finalObj = getImportJobStatusFromDatabase(initObj.getRrkBango());
        assertNotNull(finalObj);
        if (status.equals(BusinessConstants.BATCH_STATUS_COMPLETED_CODE)) {
            assertNull(finalObj.getErrorFileMei());
            assertNotNull(finalObj.getUploadKnrNchj());
        } else if (status.equals(BusinessConstants.BATCH_STATUS_SYSTEM_ERROR_CODE)) {
            assertNull(finalObj.getErrorFileMei());
            assertNull(finalObj.getUploadKnrNchj());
        } else {
            assertTrue(finalObj.getErrorFileMei() != null && finalObj.getErrorFileMei().startsWith("import-errors/error_次年度計画マスタ"));
            assertNull(finalObj.getUploadKnrNchj());
        }

        assertEquals(status, finalObj.getStts());
        assertNotNull(finalObj.getRcrdKshnNchj());
        assertNotEquals(initObj.getRcrdKshnNchj(), finalObj.getRcrdKshnNchj());

        // 更新前と更新後の比較
        assertEquals(initObj.getRrkBango(), finalObj.getRrkBango());
        assertEquals(initObj.getSystmUnyoKigyoCode(), finalObj.getSystmUnyoKigyoCode());
        assertEquals(initObj.getShainCode(), finalObj.getShainCode());
        assertEquals(initObj.getFileShbts(), finalObj.getFileShbts());
        assertEquals(initObj.getArea(), finalObj.getArea());
        assertEquals(initObj.getFileMei(), finalObj.getFileMei());
        assertEquals(initObj.getUploadKshNchj(), finalObj.getUploadKshNchj());
        assertEquals(initObj.getTrkPrgrmId(), finalObj.getTrkPrgrmId());
        assertEquals(initObj.getTrkSystmUnyoKigyoCode(), finalObj.getTrkSystmUnyoKigyoCode());
        assertEquals(initObj.getTrkShainCode(), finalObj.getTrkShainCode());
        assertEquals(initObj.getKshnPrgrmId(), finalObj.getKshnPrgrmId());
        assertEquals(initObj.getKshnSystmUnyoKigyoCode(), finalObj.getKshnSystmUnyoKigyoCode());
        assertEquals(initObj.getKshnShainCode(), finalObj.getKshnShainCode());
        assertEquals(initObj.getVrsn().toString(), finalObj.getVrsn().toString());
        assertEquals(initObj.getRcrdTrkNchj(), finalObj.getRcrdTrkNchj());

        TestDataManager.deleteTableData("T_UPLOAD_RRK", List.of(Map.of("RRK_BANGO", initObj.getRrkBango())));
    }

    /**
     * データベースからImportJobStatusを取得
     * 状態表データの検証用
     *
     * @param rrkBango 履歴番号
     */
    private ImportJobStatus getImportJobStatusFromDatabase(Long rrkBango) {
        try {
            // 真実のImportJobStatusServiceインスタンスを使用してデータベースから取得
            ImportJobStatusService importJobStatusService = new ImportJobStatusService();
            return importJobStatusService.getJobStatus(rrkBango);
        } catch (Exception e) {
            logger.error("ImportJobStatus取得エラー: rrkBango={}, error={}", rrkBango, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 実行と実行前後の履歴確認
     *
     * @param status 履歴のステータス
     */
    private void doExecute(String status) {
        // 実施前ダウンロード履歴jobを手動追加と内容検証
        var initObj = addAndCheckAndGetImportJobInfo();

        // テスト用WorkerPayloadを作成
        WorkerPayload payload=WorkerPayload.builder()
                .jobId(initObj.getRrkBango().toString())
                .operationType(BusinessConstants.OPERATION_UPLOAD_CODE)
                .request(testImportRequest)
                .userInfo(testUserInfo)
                .build();

        // executeImportTask を実行
        assertDoesNotThrow(() -> {
            dataApplicationService.executeImportTask(payload, mockLambdaContext);
        });

        // 実施後アップロード履歴jobの状態を検討
        checkImportJobInfo(initObj,status);
    }

    /**
     * テスト用インポートリクエストを作成
     */
    private ImportRequest createTestImportRequest() {
        ImportRequest importRequest = new ImportRequest();
        importRequest.setS3Key("");
        importRequest.setDataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);
        importRequest.setArea(null);
        return importRequest;
    }

    /**
     * テスト用ユーザー情報を作成
     */
    private UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("JNED01");
        userInfo.setSystemOperationCompanyCode("900001");
        userInfo.setUnitCode("11300");
        AreaInfo area = new AreaInfo("0000", "テスト用_本社");
        AreaInfo area1 = new AreaInfo("0100", "テスト用_職能部門");
        AreaInfo area2 = new AreaInfo("0200", "テスト用_");
        userInfo.setAreaInfos(List.of(area, area1, area2));
        userInfo.setAreaCode("9800");
        userInfo.setAreaName("テスト用_本社");
        userInfo.setPositionCode("99");
        userInfo.setGroupCode("0099");
        userInfo.setPositionSpecialCheck("1");
        return userInfo;
    }

    /**
     * データ取得
     */
    private List<JinendoKkk> getDatabaseData(String nendoStr, List<String> tncds )
    {
        List<JinendoKkk> results=null;
        String sql = """
                     select * from t_jinendo_kkk where nendo = ? and ssnkn_tncd IN (%s)
                     order by nendo,ssnkn_tncd
                     """;
        String tncdStr = String.join(",", Collections.nCopies(tncds.size(), "?"));
        String tncdSql = String.format(sql,tncdStr);
        Object[] params = Stream.concat(Stream.of(nendoStr), tncds.stream()).toArray();

        try (Connection connection = AuroraConnectionManager.getConnection();
             JdbcTemplate jdbcTemplate = new JdbcTemplate(connection)) {

            results = jdbcTemplate.query(tncdSql, params, rs -> {
                 String nendo = rs.getString("nendo");
                 String ssnknTncd = rs.getString("ssnkn_tncd");
                 String groupCode = rs.getString("group_code");
                 String areaCode = rs.getString("area_code");
                 String unitCode = rs.getString("unit_code");
                 String ctgryCode = rs.getString("ctgry_code");
                 String kigyoCode = rs.getString("kigyo_code");
                 String gytShkNo = rs.getString("gyt_shk_no");
                 String subCtgryCode = rs.getString("sub_ctgry_code");
                 String systmUnyoKigyoCode = rs.getString("systm_unyo_kigyo_code");
                 String tntshMei = rs.getString("tntsh_mei");
                 String gytmKanji = rs.getString("gytm_kanji");
                 String hnkgTrkmKubun = rs.getString("hnkg_trkm_kubun");
                 String iknskAreaCode = rs.getString("iknsk_area_code");
                 String iknskGroupCode = rs.getString("iknsk_group_code");
                 String iknskUnitCode = rs.getString("iknsk_unit_code");
                 String ssnKanriTnmKanji = rs.getString("ssn_kanri_tnm_kanji");
                 String tkyAreaCode = rs.getString("tky_area_code");
                 String tkyGroupCode = rs.getString("tky_group_code");
                 String tkyUnitCode = rs.getString("tky_unit_code");
                 String trkPrgrmId = rs.getString("trk_prgrm_id");
                 String trkSystmUnyoKigyoCode = rs.getString("trk_systm_unyo_kigyo_code");
                 String trkShainCode = rs.getString("trk_shain_code");
                 String kshnPrgrmId = rs.getString("kshn_prgrm_id");
                 String kshnSystmUnyoKigyoCode = rs.getString("kshn_systm_unyo_kigyo_code");
                 String kshnShainCode = rs.getString("kshn_shain_code");
                 String vrsn = rs.getString("vrsn");
                 String rcrdTrkNchj = rs.getString("rcrd_trk_nchj");
                 String rcrdKshnNchj = rs.getString("rcrd_kshn_nchj");
                 return new JinendoKkk(nendo,ssnknTncd,groupCode,areaCode,unitCode,ctgryCode,kigyoCode,gytShkNo,subCtgryCode,systmUnyoKigyoCode,tntshMei,gytmKanji,hnkgTrkmKubun,
                         iknskAreaCode,iknskGroupCode,iknskUnitCode,ssnKanriTnmKanji,tkyAreaCode,tkyGroupCode,tkyUnitCode,trkPrgrmId,trkSystmUnyoKigyoCode,trkShainCode,kshnPrgrmId,
                         kshnSystmUnyoKigyoCode,kshnShainCode,vrsn,rcrdTrkNchj,rcrdKshnNchj);
            });

        } catch (SQLException e) {
            e.printStackTrace();
        }
        return results;
    }

    private record JinendoKkk(
            String nendo,String ssnknTncd,String groupCode,String areaCode,String unitCode,String ctgryCode,String kigyoCode,String gytShkNo,String subCtgryCode,
            String systmUnyoKigyoCode,String tntshMei,String gytmKanji,String hnkgTrkmKubun,String iknskAreaCode,String iknskGroupCode,String iknskUnitCode,
            String ssnKanriTnmKanji,String tkyAreaCode,String tkyGroupCode,String tkyUnitCode,String trkPrgrmId,String trkSystmUnyoKigyoCode,String trkShainCode,
            String kshnPrgrmId,String kshnSystmUnyoKigyoCode,String kshnShainCode,String vrsn,String rcrdTrkNchj,String rcrdKshnNchj
    ) {}
}