package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.util.TestDataManager;
import com.ms.bp.util.CsvContentComparator;
import com.ms.bp.shared.util.DateUtil;
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.MockedStatic;
import org.mockito.quality.Strictness;
import static org.mockito.Mockito.*;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 次年度計画マスタエクスポート機能の集成テスト
 * CsvContentComparatorを使用してCSV内容を直接比較検証
 */
public class PlanMasterExportTaskNormalTest {

    private static final Logger logger = LoggerFactory.getLogger(PlanMasterExportTaskNormalTest.class);

    private DataApplicationService dataApplicationService;
    private TestDataManager planMasterExportTestDataManager;
    private Map<String, List<Map<String, Object>>> insertedDataTracker;

    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;

    @Mock
    private S3Service mockS3Service;

    @Mock
    private Context mockLambdaContext;

    private MockedStatic<DateUtil> mockedDateUtil;

    @BeforeEach
    void setUp() {
        logger.info("=== executeExportTask集成テストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // 全スレッド対応の静的mockを設定
            setupGlobalMocks();

            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");
            System.setProperty("WORKER_FUNCTION_NAME", "test-worker-function");

            // 被测试服务の初期化
            dataApplicationService = new DataApplicationService();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // S3Serviceもモックに置き換え（FileProcessingServiceとTaskOrchestrationService内のS3Service）
            // DataApplicationService内のTaskOrchestrationServiceのS3Serviceを置き換える必要がある
            Field taskOrchestrationServiceField = DataApplicationService.class.getDeclaredField("taskOrchestrationService");
            taskOrchestrationServiceField.setAccessible(true);
            Object taskOrchestrationService = taskOrchestrationServiceField.get(dataApplicationService);

            // TaskOrchestrationService内のFileExportOrchestratorを取得
            Field fileExportOrchestratorField = taskOrchestrationService.getClass().getDeclaredField("fileExportOrchestrator");
            fileExportOrchestratorField.setAccessible(true);
            Object fileExportOrchestrator = fileExportOrchestratorField.get(taskOrchestrationService);

            // FileExportOrchestrator内のFileProcessingServiceを取得
            Field fileProcessingServiceField = fileExportOrchestrator.getClass().getDeclaredField("fileProcessingService");
            fileProcessingServiceField.setAccessible(true);
            Object fileProcessingService = fileProcessingServiceField.get(fileExportOrchestrator);

            // FileProcessingService内のS3Serviceを置き換え
            Field s3ServiceField = fileProcessingService.getClass().getDeclaredField("s3Service");
            s3ServiceField.setAccessible(true);
            s3ServiceField.set(fileProcessingService, mockS3Service);

            // 次年度計画マスタエクスポート用Excel テストデータ管理器の初期化
            planMasterExportTestDataManager = new TestDataManager("plan_master_export_test_data.xlsx");

            // Excel からテストデータを読み込んでデータベースに挿入
            insertedDataTracker = planMasterExportTestDataManager.insertAllTestData();

            // Mock の基本設定
            setupMockBehaviors();

            logger.info("=== executeExportTask集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("テストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("テストセットアップに失敗しました", e);
        }
    }

    /**
     * 全スレッド対応の静的mockを設定
     * 線程池で実行されるタスクでもmockが有効になるようにLENIENTモードを使用
     */
    private void setupGlobalMocks() {
        // DateUtil の静的mock を全スレッド対応で設定
        mockedDateUtil = mockStatic(DateUtil.class, withSettings().strictness(Strictness.LENIENT));

        // 固定の日時文字列を返すように設定（線程池内でも有効）
        String fixedDateTime = "20241201120000";
        String fixedDate = "202412013012";  // CURRENT_DATE用（有効期間内の日付）

        mockedDateUtil.when(DateUtil::getCurrentDateTimeString)
                .thenReturn(fixedDateTime);
        mockedDateUtil.when(DateUtil::getCurrentDateTimeString_YYYYMMDDHHMM).thenReturn(fixedDate);
        // 测试数据の年度（2025）を返却
        mockedDateUtil.when(DateUtil::getNextFiscalYear)
                .thenReturn("2025");

        logger.info("グローバルMockを設定しました: DateTime={}, Date={}, FiscalYear=2025",
                fixedDateTime, fixedDate);
    }

    @AfterEach
    void tearDown() {
        logger.info("=== executeExportTask集成テストクリーンアップ開始 ===");

        if (insertedDataTracker != null) {
            logger.info("テストデータクリーンアップを実行します: {} テーブル", insertedDataTracker.size());
            planMasterExportTestDataManager.deleteAllTestData(insertedDataTracker);
            logger.info("テストデータクリーンアップが完了しました");
        }

        try {
            // 静的mockをクローズ
            if (mockedDateUtil != null) {
                mockedDateUtil.close();
                logger.debug("DateUtil mockをクローズしました");
            }
        } catch (Exception e) {
            logger.error("Mock クローズ中にエラーが発生しました: {}", e.getMessage(), e);
        }
        logger.info("=== executeExportTask集成テストクリーンアップ完了 ===");
    }

    /**
     * 次年度計画マスタエクスポート処理テスト
     * エリア9002の23フィールド詳細検証
     */
    @Test
    @DisplayName("次年度計画マスタエクスポート処理_エリア9002_23フィールド詳細検証")
    void testExecuteExportTask_次年度計画マスタエクスポート処理() {
        logger.info("=== エリア9002次年度計画マスタエクスポート詳細検証テスト開始 ===");

        try {
            // DateUtil mock設定は setupGlobalMocks() で既に設定済み

            // テスト用エリアコード
            String testAreaCode = "9002";
            logger.info("テスト用エリアコード: {}", testAreaCode);

            // WorkerPayload作成
            WorkerPayload payload = createPlanMasterExportPayload("1001", Arrays.asList(testAreaCode));

            // executeExportTask実行（詳細ログ出力付き）
            logger.info("executeExportTask実行開始...");
            try {
                dataApplicationService.executeExportTask(payload, mockLambdaContext);
                logger.info("executeExportTask実行完了");
            } catch (Exception e) {
                logger.error("executeExportTask実行中にエラーが発生: {}", e.getMessage(), e);
                throw e;
            }

            // エリア9002の23フィールド詳細CSV内容検証
            verifyCsvContent(Arrays.asList(testAreaCode));

            logger.info("=== エリア9002次年度計画マスタエクスポート詳細検証テスト完了 ===");

        } finally {
            try {
                logger.warn("テスト失敗によりデータクリーンアップを実行します");
                if (insertedDataTracker != null) {
                    planMasterExportTestDataManager.deleteAllTestData(insertedDataTracker);
                    logger.info("テスト失敗時のデータクリーンアップが完了しました");
                }
            } catch (Exception cleanupException) {
                logger.error("テスト失敗時のデータクリーンアップでエラーが発生しました: {}", cleanupException.getMessage(), cleanupException);
            }
        }
    }
    /**
     * 業務データ異常テスト - データ不存在
     * 指定された条件に該当するデータが存在しない場合の処理を検証
     * エラーCSVファイルの内容を詳細に検証
     */
    @Test
    @Order(3)
    @DisplayName("業務データ異常_データ不存在_エラーCSV内容検証")
    void testExecuteExportTask_業務データ異常_データ不存在() {
        logger.info("=== 業務データ異常テスト開始：データ不存在 ===");

        try {
            // 存在しないエリアコードでPayload作成
            List<String> nonExistentAreaCodes = Arrays.asList("9999"); // 存在しないエリアコード
            WorkerPayload payload = createPlanMasterExportPayload("1003", nonExistentAreaCodes);

            // executeExportTask実行（データ不存在でも正常終了する想定）
            assertDoesNotThrow(() -> {
                dataApplicationService.executeExportTask(payload, mockLambdaContext);
            });

            // エラーCSV内容の詳細検証
            validateErrorCsvContent(nonExistentAreaCodes);

            logger.info("=== 業務データ異常テスト完了：データ不存在 ===");

        } catch (Exception e) {
            logger.error("データ不存在テストエラー: {}", e.getMessage(), e);
            fail("データ不存在テストに失敗しました: " + e.getMessage());
        }
    }

    // ==================== パフォーマンステスト ====================

    /**
     * パフォーマンス異常テスト - 大量データ処理時の異常
     * 大量データ処理時のメモリ使用量やパフォーマンス異常を検証
     */
    @Test
    @Order(2)
    @DisplayName("パフォーマンス_大量データ処理")
    void testExecuteExportTask_パフォーマンス_大量データ処理() {
        logger.info("=== パフォーマンステスト開始：大量データ処理 ===");

        try {
            // 大量データを想定したPayload作成
            List<String> manyAreaCodes = Arrays.asList("0203", "0400", "0500", "0631", "0632",
                    "0700", "0800", "0900", "1000", "1300",
                    "1200", "1400", "0202", "0204", "1800","1500", "0207", "0208", "1600");
            WorkerPayload payload = createPlanMasterExportPayload("1001", manyAreaCodes);

            // executeExportTask実行
            assertDoesNotThrow(() -> {
                dataApplicationService.executeExportTask(payload, mockLambdaContext);
            });

            logger.info("=== パフォーマンステスト完了：大量データ処理 ===");

        } catch (Exception e) {
            logger.error("パフォーマンステストエラー: {}", e.getMessage(), e);
        }
    }

    // ==================== プライベートメソッド ====================

    /**
     * エリア9002の詳細CSV内容検証（23フィールド全て）
     */
    private void verifyCsvContent(List<String> expectedAreaCodes) {
        logger.info("=== エリア9002詳細CSV内容検証開始 ===");

        try {
            // エリア9002の期待データ（23フィールド全て）
            List<List<String>> expectedDataRows = Arrays.asList(
                    Arrays.asList(
                            "9002",
                            "テスト大阪エリア",
                            "0003",
                            "",
                            "G002",
                            "53100",
                            "000担当者名漢字",
                            "1118888",
                            "1118888企業名漢字",
                            "",
                            "",
                            "0000",
                            "",
                            "1118888",
                            "採算管理単位名2",
                            "特別取組",
                            "変更後取組2",
                            "",
                            "",
                            "",
                            "",
                            "300",
                            "500"
                    ),
                    Arrays.asList(
                            "9002",                    // 1. エリアCD
                            "テスト大阪エリア",          // 2. エリア名
                            "T002",                    // 3. カテCD
                            "低温",               // 4. カテゴリ
                            "G002",             // 5. グループ
                            "U002 ",             // 6. ユニット
                            "鈴木三郎",               // 7. 担当者
                            "K00004 ",                 // 8. 企業CD
                            "企業004株式会社",         // 9. 企業名
                            "04",                       // 10. 業態集計
                            "",              // 11. 業態名
                            "S002",                     // 12. サブカテゴリ
                            "チルド食品",                       // 13. サブカテゴリ名
                            "1003002",                // 14. 採算管理単位CD
                            "SKSA採算管理単位2",       // 15. 採算管理単位名
                            "自由集約1-1",                       // 16. 変更前取組区分
                            "SKSA処理2",              // 17. 変更後取組区分
                            "9001",                       // 18. 移管先エリアCD
                            "テスト東京エリア",                       // 19. 移管先エリア名
                            "G001",                       // 20. 移管先グループCD
                            "U001",                       // 21. 移管先ユニットCD
                            "2700000",                // 22. 当年度実績累計(参考) 1080000+1620000
                            "3000000"                 // 23. 当年度計画累計(参考) 1200000+1800000
                    ),
                    Arrays.asList(
                            "9002",
                            "テスト大阪エリア",
                            "T002",
                            "低温",
                            "G002",
                            "U002 ",
                            "SKSA花子",
                            "K00010 ",
                            "",
                            "03",
                            "百貨店",
                            "S002",
                            "チルド食品",
                            "2001001",
                            "UNION第2分支テスト1",
                            "自由集約1","","","","","","0","0"
                    ),
                    Arrays.asList(
                            "9002","テスト大阪エリア","","","G002","U002 ","","","","","","","","1003001","SKSA採算管理単位1","","SKSA処理1","9003","テスト名古屋エリア","G003","U003","0","0"
                    )
            );

            // 全フィールド詳細比較検証
            CsvContentComparator.CsvValidationConfig detailedConfig =
                    CsvContentComparator.createPlanMasterConfigWithData(expectedAreaCodes, expectedDataRows);

            CsvContentComparator.validateCsvDataComparison(mockS3Service, detailedConfig);
            logger.info("✅ エリア9002詳細CSV内容検証完了（23フィールド全て検証済み）");

        } catch (Exception e) {
            logger.error("エリア9002詳細CSV内容検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("エリア9002のCSV詳細検証に失敗しました", e);
        }
    }

    /**
     * エラーCSV内容検証
     * データが存在しない場合のCSVファイル内容を詳細に検証
     *
     * @param expectedAreaCodes 検索対象のエリアコードリスト
     */
    private void validateErrorCsvContent(List<String> expectedAreaCodes) {
        logger.info("=== エラーCSV内容検証開始：エリア={} ===", expectedAreaCodes);

        try {
            // エラーCSV検証用の設定を作成
            CsvContentComparator.CsvValidationConfig errorConfig =
                    new CsvContentComparator.CsvValidationConfig(
                            null,
                            "次年度計画マスタ",
                            "事業計画",
                            "条件に一致するデータが取得できませんでした。", // データ比較なし
                            null, // 全フィールド
                              null,
                            null// デフォルトエラーメッセージ
                             );


            // エラーCSV内容の検証を実行
            CsvContentComparator.validateErrorCsvContent(mockS3Service, errorConfig);

            logger.info("✅ エラーCSV内容検証完了：データ不存在の状態が正常に処理されています");
            logger.warn("注意: 指定されたエリアコード {} に対応するデータが存在しません", expectedAreaCodes);

        } catch (Exception e) {
            logger.error("エラーCSV内容検証エラー: {}", e.getMessage(), e);
            throw new RuntimeException("エラーCSV内容検証に失敗しました", e);
        }
    }

    /**
     * Mock オブジェクトの基本動作を設定
     */
    private void setupMockBehaviors() {
        try {
            // AsyncLambdaInvoker の Mock 設定
            doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any(WorkerPayload.class));

            // S3Service の Mock 設定
            when(mockS3Service.uploadFileFromStream(any(), anyString(), anyString(), anyLong(), any()))
                    .thenReturn(Map.of("success", true, "key", "out/jinendoKkkMst/次年度計画マスタ_20241201120000.zip"));

            when(mockS3Service.getSignedDownloadUrl(anyString(), anyLong()))
                    .thenReturn("https://test-s3-url.com/download");

            when(mockS3Service.getObjectMetadata(anyString()))
                    .thenReturn(Map.of("fileSize", 1024L));

            // Lambda Context の Mock 設定
            when(mockLambdaContext.getRemainingTimeInMillis()).thenReturn(900000); // 15分（BackgroundTimeoutMonitorの緩衝時間10分より大きく設定）
            when(mockLambdaContext.getAwsRequestId()).thenReturn("test-request-id");

            logger.debug("Mock オブジェクトの基本動作設定完了");
        } catch (Exception e) {
            logger.error("Mock 設定エラー: {}", e.getMessage(), e);
            throw new RuntimeException("Mock 設定に失敗しました", e);
        }
    }

    /**
     * 次年度計画マスタエクスポート用WorkerPayloadを作成
     */
    private WorkerPayload createPlanMasterExportPayload(String jobId, List<String> areaList) {
        ExportRequest exportRequest = new ExportRequest();
        exportRequest.setDataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);
        exportRequest.setArea(areaList);
        exportRequest.setHnshBashoKubun("0"); // 本社
        exportRequest.setDataKubun(Arrays.asList("0")); // 移管前
        exportRequest.setCtgryKubun("1"); // 加食

        UserInfo userInfo = createTestUserInfo("TEST01", "100001");

        return WorkerPayload.builder()
                .jobId(jobId)
                .operationType(BusinessConstants.OPERATION_DOWNLOAD_CODE)
                .request(exportRequest)
                .userInfo(userInfo)
                .build();
    }

    /**
     * テスト用ユーザー情報を作成
     */
    private UserInfo createTestUserInfo(String shainCode, String systemOperationCompanyCode) {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode(shainCode);
        userInfo.setSystemOperationCompanyCode(systemOperationCompanyCode);
        return userInfo;
    }
}
